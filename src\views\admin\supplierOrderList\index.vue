<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
					<el-form-item label="订单编号" prop="name">
						<el-input placeholder="请输入订单编号" v-model="state.queryForm.name" />
					</el-form-item>
          <el-form-item label="商品名称" prop="name">
            <el-input placeholder="请输入商品名称" v-model="state.queryForm.name" />
          </el-form-item>
          <el-form-item label="采购单位" prop="name">
            <el-input placeholder="请输入采购单位" v-model="state.queryForm.name" />
          </el-form-item>
          <el-form-item label="预计送达日期" prop="name">
            <el-date-picker
                v-model="state.queryForm.name"
                type="daterange"
                range-separator="→"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :size="size"
            />
          </el-form-item>
          <el-form-item label="收货人" prop="name">
            <el-input placeholder="请输入收货人" v-model="state.queryForm.name" />
          </el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
						<el-button icon="Refresh" @click="resetQuery">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>
      <el-card>
        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane v-for="(item,index) in tabList" :label="item.label" :name="item.name" :key="index">
            <el-card v-for="i in 10" :key="i" style="margin-bottom: 20px">
              <div class="demo-tabs-content">
                <el-tag type="success" style="margin-right: 10px">非加工</el-tag>
                <el-tag type="success" style="margin-right: 20px">供应链分拣</el-tag>
                <div style="color: #b6bec5;margin-right: 20px;display: flex;align-items: center"><el-icon><Tickets /></el-icon>订单编号：<span style="color: #353a3f">202507261735083852</span><el-icon style="color:#04c66e;"><CopyDocument /></el-icon></div>
                <div style="color: #b6bec5;margin-right: 20px;display: flex;align-items: center"><el-icon><Tickets /></el-icon>配送单位：<span style="color: #353a3f">测试供应商（18210124586）</span><el-icon style="color:#04c66e;"><CopyDocument /></el-icon></div>

                <div style="color: #b6bec5;margin-right: 20px;display: flex;align-items: center"><el-icon><Clock /></el-icon>预计送达时间：<span style="color: #353a3f">2025-07-29 04:12:12（星期二）</span></div>

                <div style="color: #b6bec5;margin-right: 10px;display: flex;align-items: center"><el-icon><Clock /></el-icon>确认收货时间：<span style="color: #353a3f">2025-07-26 04:12:12（星期六）</span></div>

              </div>

              <div style="display: flex;justify-content: space-between;padding: 10px 20px;align-items: center;border-bottom: 1px solid #dadada">
                <div style="display: flex;align-items: center">
                  <img src="../../../assets/img1.png" style="width: 80px;border-radius: 5px;margin-right: 20px" alt="">
                  <div>
                    <div style="margin-bottom: 10px">五花肉  <span style="color: red">￥12</span></div>
                    <div style="display: flex;align-items: center">
                      <el-icon style="margin-right: 5px"><Tickets /></el-icon> <span>订单量 28kg</span>&nbsp;&nbsp;&nbsp;&nbsp;
                      <el-icon style="margin-right: 5px"><Tickets /></el-icon> <span>验收量 30kg</span>&nbsp;&nbsp;&nbsp;&nbsp;
                      <el-icon style="margin-right: 5px"><Tickets /></el-icon> <span>发货量 30kg</span>
                    </div>
                  </div>
                </div>
                <div style="color: #00c56b">
                  <el-button type="success" text>详情</el-button>
                </div>
              </div>

              <div style="display: flex;padding: 20px 0 0 20px">
                <el-descriptions style="margin-right: 50px">
                  <el-descriptions-item label="下单时间">2025-07-26 04:12:12<</el-descriptions-item>
                  <el-descriptions-item label="用户">李先生</el-descriptions-item>
                  <el-descriptions-item label="收货地址">生态文旅区徐杨路77号仓库中心</el-descriptions-item>
                  <el-descriptions-item label="轨迹">
										<el-button type="success" style="color: #00c56b;" link @click="showMapLine">点击查看轨迹</el-button>
                  </el-descriptions-item>
                  <el-descriptions-item label="电话">18210123658</el-descriptions-item>
                </el-descriptions>
                <div style="background: #f8fffc;padding: 10px 20px;display: flex;align-items: center">
                  <div style="margin-right: 20px">
                    <div style="color: #b6bec5;"><el-icon style="margin-right: 10px"><Tickets /></el-icon>应收金额    <span style="color: #353A3FFF">￥336</span></div>
                    <div style="color: #b6bec5;"><el-icon style="margin-right: 10px"><Tickets /></el-icon>实收金额    <span style="color: #353A3FFF">￥336</span></div>
                  </div>

                  <div style="color: #b6bec5;margin-right: 20px"><el-icon style="margin-right: 10px"><Tickets /></el-icon>订单状态    <span style="color: #353A3FFF">完成</span></div>
                  <div style="color: #b6bec5;margin-right: 20px"><el-icon style="margin-right: 10px"><Tickets /></el-icon>结算状态      <span style="color: red">待对账</span></div>

                </div>
              </div>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </el-card>

			<el-dialog v-model="dialogTableVisible" width="1000">
				<div style="width: 1000px;height: 700px;background: #00c56b" id="container"></div>
			</el-dialog>
		</div>

	</div>
</template>

<script setup lang="ts" name="supplierOrderList">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList } from '/@/api/admin/organization';
import AMapLoader from "@amap/amap-jsapi-loader";
import { onMounted, onUnmounted } from 'vue';

let map: any = null;

const activeName = ref('first')
const dialogTableVisible = ref(false)

const tabList = ref([
  {label: '全部（117）', name: 'first'},
  {label: '待供应商发货', name: '1'},
  {label: '待平台验收', name: '2'},
  {label: '分拣中', name: '3'},
  {label: '待平台发货', name: '4'},
  {label: '待配送', name: '5'},
  {label: '配送中', name: '6'},
  {label: '待采购单位验收', name: '7'},
  {label: '交易关闭', name: '8'},
  {label: '订单取消', name: '9'},
  {label: '拒收', name: '10'},
  {label: '完成', name: '11'},
])


// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	pageList: fetchList,
});

//  table hook
const { getDataList } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	getDataList();
};

const showMapLine = () => {
	dialogTableVisible.value = true;
	window._AMapSecurityConfig = {
		securityJsCode: "c6546ef29c2e4fbc940ec16bfc298079",
	};
	AMapLoader.load({
		key: "88ed2d3e65df8fec0f82307ff9ca1200", // 申请好的Web端开发者Key，首次调用 load 时必填
		version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
		plugins: ["AMap.Scale", "AMap.MoveAnimation"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
	})
		.then((AMap) => {

			let marker: any, lineArr = [[116.478935,39.997761],[116.478939,39.997825],[116.478912,39.998549],[116.478912,39.998549],[116.478998,39.998555],[116.478998,39.998555],[116.479282,39.99856],[116.479658,39.998528],[116.480151,39.998453],[116.480784,39.998302],[116.480784,39.998302],[116.481149,39.998184],[116.481573,39.997997],[116.481863,39.997846],[116.482072,39.997718],[116.482362,39.997718],[116.483633,39.998935],[116.48367,39.998968],[116.484648,39.999861]];

			map = new AMap.Map("container", {
				// 设置地图容器id
				viewMode: "2D", // 是否为3D地图模式
				center: [116.397428, 39.90923],
				zoom: 17
			});
			marker = new AMap.Marker({
				map: map,
				position: [116.478935,39.997761],
				icon: "https://a.amap.com/jsapi_demos/static/demo-center-v2/car.png",
				offset: new AMap.Pixel(-13, -26),
			});

			// 绘制轨迹
			let polyline = new AMap.Polyline({
				map: map,
				path: lineArr,
				showDir:true,
				strokeColor: "#28F",  //线颜色
				strokeWeight: 6,      //线宽
			});

			let passedPolyline = new AMap.Polyline({
				map: map,
				strokeColor: "#AF5",  //线颜色
				strokeWeight: 6,      //线宽
			});

			marker.on('moving', function (e: any) {
				passedPolyline.setPath(e.passedPath);
				map.setCenter(e.target.getPosition(),true)
			});

			map.setFitView();

			setTimeout(()=>{
				marker.moveAlong(lineArr, {
					// 每一段的时长
					duration: 500,//可根据实际采集时间间隔设置
					// JSAPI2.0 是否延道路自动设置角度在 moveAlong 里设置
					autoRotation: true,
				});
			},3000)


		})
		.catch((e) => {
			console.log(e);
		});
};

onMounted(() => {

});

onUnmounted(() => {
	map?.destroy();
});

</script>
<style lang="scss" scoped>
.layout-padding {

  .demo-tabs-content{
    background: #f6fffa;
    height: 40px;
    padding: 0 20px;
    display: flex;
    align-items: center;
  }

  :deep .el-tabs__item.is-active,:deep .el-tabs__item:hover {
    color: #00c56b;
  }
  :deep .el-tabs__active-bar{
    background-color: #00c56b;
  }
}
</style>

<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
					<el-form-item label="供应商名称" prop="supplierName">
						<el-input placeholder="请输入供应商名称" v-model="state.queryForm.supplierName" />
					</el-form-item>
					<el-form-item label="商品名称" prop="productName">
						<el-input placeholder="请输入商品名称" v-model="state.queryForm.productName" />
					</el-form-item>
					<el-form-item label="商品分类" prop="category">
						<el-select placeholder="请选择商品分类" v-model="state.queryForm.category" clearable>
							<el-option label="蔬菜类" value="vegetables" />
							<el-option label="肉类" value="meat" />
							<el-option label="水果类" value="fruits" />
							<el-option label="调料类" value="seasoning" />
							<el-option label="粮油类" value="grains" />
						</el-select>
					</el-form-item>
					<el-form-item label="报价状态" prop="quoteStatus">
						<el-select placeholder="请选择报价状态" v-model="state.queryForm.quoteStatus" clearable>
							<el-option label="待报价" value="pending" />
							<el-option label="已报价" value="quoted" />
							<el-option label="已确认" value="confirmed" />
							<el-option label="已拒绝" value="rejected" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
						<el-button icon="Refresh" @click="resetQuery">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>

			<!-- 状态标签栏 -->
			<el-row class="mb15">
				<el-tag
					v-for="(item, index) in statusTabs"
					:key="index"
					:type="activeStatus === item.value ? 'success' : 'info'"
					:effect="activeStatus === item.value ? 'dark' : 'plain'"
					class="status-tag"
					@click="handleStatusChange(item.value)"
				>
					{{ item.label }}
				</el-tag>
			</el-row>

			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()">
						新增报价
					</el-button>
					<el-button plain :disabled="multiple" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
						删除
					</el-button>
					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'quote_export'"
						@exportExcel="exportExcel"
						class="ml10 mr20"
						style="float: right"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>
			<el-table
				:data="filteredTableData"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="selectionChangHandle"
			>
				<el-table-column type="selection" width="55" align="center" />
				<el-table-column prop="supplierName" label="供应商名称" show-overflow-tooltip />
				<el-table-column prop="productName" label="商品名称" show-overflow-tooltip />
				<el-table-column prop="category" label="商品分类" width="100">
					<template #default="scope">
						<el-tag :type="getCategoryTag(scope.row.category)">{{ getCategoryText(scope.row.category) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="specification" label="规格" width="120" />
				<el-table-column prop="unit" label="单位" width="80" />
				<el-table-column prop="quantity" label="数量" width="100" />
				<el-table-column prop="unitPrice" label="单价(元)" width="120">
					<template #default="scope">
						<span class="price-text">¥{{ scope.row.unitPrice }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="totalPrice" label="总价(元)" width="120">
					<template #default="scope">
						<span class="amount-text">¥{{ scope.row.totalPrice }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="quoteStatus" label="报价状态" width="100">
					<template #default="scope">
						<el-tag :type="getQuoteStatusTag(scope.row.quoteStatus)">{{ getQuoteStatusText(scope.row.quoteStatus) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="quoteTime" label="报价时间" width="160" />
				<el-table-column label="操作" width="200">
					<template #default="scope">
						<el-button type="success" link @click="formDialogRef.openDialog(scope.row.id)">编辑</el-button>
						<el-button type="success" link @click="viewDetail(scope.row)">查看</el-button>
						<el-button type="success" link @click="handleDelete([scope.row.id])">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="quoteList">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));

// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);
// 当前状态
const activeStatus = ref('all');

// 状态标签
const statusTabs = ref([
	{ label: '全部', value: 'all' },
	{ label: '待报价', value: 'pending' },
	{ label: '已报价', value: 'quoted' },
	{ label: '已确认', value: 'confirmed' },
	{ label: '已拒绝', value: 'rejected' }
]);

// 模拟数据
const tableData = ref([
	{
		id: '1',
		supplierName: '绿源农业合作社',
		productName: '有机白菜',
		category: 'vegetables',
		specification: '500g/包',
		unit: '包',
		quantity: 100,
		unitPrice: 8.50,
		totalPrice: 850.00,
		quoteStatus: 'confirmed',
		quoteTime: '2025-08-01 09:30:00'
	},
	{
		id: '2',
		supplierName: '鲜美肉业有限公司',
		productName: '精品猪肉',
		category: 'meat',
		specification: '1kg/份',
		unit: '份',
		quantity: 50,
		unitPrice: 28.00,
		totalPrice: 1400.00,
		quoteStatus: 'quoted',
		quoteTime: '2025-08-01 10:15:00'
	},
	{
		id: '3',
		supplierName: '阳光果园',
		productName: '新鲜苹果',
		category: 'fruits',
		specification: '2kg/箱',
		unit: '箱',
		quantity: 80,
		unitPrice: 15.00,
		totalPrice: 1200.00,
		quoteStatus: 'confirmed',
		quoteTime: '2025-08-01 11:20:00'
	},
	{
		id: '4',
		supplierName: '香满园调料厂',
		productName: '生抽酱油',
		category: 'seasoning',
		specification: '500ml/瓶',
		unit: '瓶',
		quantity: 30,
		unitPrice: 12.80,
		totalPrice: 384.00,
		quoteStatus: 'pending',
		quoteTime: ''
	},
	{
		id: '5',
		supplierName: '金穗粮油公司',
		productName: '优质大米',
		category: 'grains',
		specification: '5kg/袋',
		unit: '袋',
		quantity: 60,
		unitPrice: 25.00,
		totalPrice: 1500.00,
		quoteStatus: 'quoted',
		quoteTime: '2025-08-01 14:30:00'
	},
	{
		id: '6',
		supplierName: '绿源农业合作社',
		productName: '新鲜胡萝卜',
		category: 'vegetables',
		specification: '1kg/袋',
		unit: '袋',
		quantity: 120,
		unitPrice: 6.50,
		totalPrice: 780.00,
		quoteStatus: 'rejected',
		quoteTime: '2025-08-01 15:45:00'
	},
	{
		id: '7',
		supplierName: '鲜美肉业有限公司',
		productName: '优质牛肉',
		category: 'meat',
		specification: '500g/份',
		unit: '份',
		quantity: 40,
		unitPrice: 45.00,
		totalPrice: 1800.00,
		quoteStatus: 'confirmed',
		quoteTime: '2025-08-01 16:20:00'
	},
	{
		id: '8',
		supplierName: '阳光果园',
		productName: '香甜橙子',
		category: 'fruits',
		specification: '3kg/箱',
		unit: '箱',
		quantity: 70,
		unitPrice: 18.00,
		totalPrice: 1260.00,
		quoteStatus: 'quoted',
		quoteTime: '2025-08-02 08:30:00'
	},
	{
		id: '9',
		supplierName: '香满园调料厂',
		productName: '精制食盐',
		category: 'seasoning',
		specification: '500g/包',
		unit: '包',
		quantity: 25,
		unitPrice: 3.50,
		totalPrice: 87.50,
		quoteStatus: 'pending',
		quoteTime: ''
	},
	{
		id: '10',
		supplierName: '金穗粮油公司',
		productName: '花生油',
		category: 'grains',
		specification: '5L/桶',
		unit: '桶',
		quantity: 20,
		unitPrice: 85.00,
		totalPrice: 1700.00,
		quoteStatus: 'confirmed',
		quoteTime: '2025-08-02 10:15:00'
	}
]);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	loading: false,
	pagination: {
		total: tableData.value.length,
		current: 1,
		size: 10
	}
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 根据状态过滤数据
const filteredTableData = computed(() => {
	if (activeStatus.value === 'all') {
		return tableData.value;
	}
	return tableData.value.filter(item => item.quoteStatus === activeStatus.value);
});

// 状态切换
const handleStatusChange = (status: string) => {
	activeStatus.value = status;
};

// 获取商品分类标签颜色
const getCategoryTag = (category: string) => {
	const tagMap: Record<string, string> = {
		vegetables: 'success',
		meat: 'danger',
		fruits: 'warning',
		seasoning: 'info',
		grains: 'primary'
	};
	return tagMap[category] || 'default';
};

// 获取商品分类文本
const getCategoryText = (category: string) => {
	const textMap: Record<string, string> = {
		vegetables: '蔬菜类',
		meat: '肉类',
		fruits: '水果类',
		seasoning: '调料类',
		grains: '粮油类'
	};
	return textMap[category] || '未知';
};

// 获取报价状态标签颜色
const getQuoteStatusTag = (status: string) => {
	const tagMap: Record<string, string> = {
		pending: 'info',
		quoted: 'warning',
		confirmed: 'success',
		rejected: 'danger'
	};
	return tagMap[status] || 'default';
};

// 获取报价状态文本
const getQuoteStatusText = (status: string) => {
	const textMap: Record<string, string> = {
		pending: '待报价',
		quoted: '已报价',
		confirmed: '已确认',
		rejected: '已拒绝'
	};
	return textMap[status] || '未知';
};

// 查看详情
const viewDetail = (row: any) => {
	useMessage().info(`查看 ${row.supplierName} - ${row.productName} 的详细信息`);
};

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	// 重新加载数据
	state.loading = false;
};

// 导出excel
const exportExcel = () => {
	useMessage().success('导出功能开发中...');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除选中的报价记录，是否继续？');
	} catch {
		return;
	}

	try {
		// 模拟删除操作
		tableData.value = tableData.value.filter(item => !ids.includes(item.id));
		state.pagination.total = tableData.value.length;
		useMessage().success('删除成功');
		// 清空选择
		selectObjs.value = [];
		multiple.value = true;
	} catch (err: any) {
		useMessage().error('删除失败');
	}
};
</script>
<style lang="scss" scoped>
.layout-padding {

	// 状态标签样式
	.status-tag {
		margin-right: 10px;
		margin-bottom: 10px;
		cursor: pointer;
		transition: all 0.3s;

		&:hover {
			transform: translateY(-1px);
		}
	}

	// 金额文本样式
	.amount-text {
		color: #00c56b;
		font-weight: 600;
	}

	// 价格文本样式
	.price-text {
		color: #409eff;
		font-weight: 500;
	}

	// 参考供应链页面的样式
	:deep(.el-button--text.el-button--success) {
		color: #00c56b;

		&:hover {
			color: #00a85a;
		}
	}

	:deep(.el-tag--primary) {
		background-color: #ecf5ff;
		border-color: #d9ecff;
		color: #409eff;
	}

	:deep(.el-tag--success) {
		background-color: #f0f9ff;
		border-color: #c6f6d5;
		color: #00c56b;
	}

	:deep(.el-tag--warning) {
		background-color: #fdf6ec;
		border-color: #faecd8;
		color: #e6a23c;
	}

	:deep(.el-tag--info) {
		background-color: #f4f4f5;
		border-color: #e9e9eb;
		color: #909399;
	}

	:deep(.el-tag--danger) {
		background-color: #fef0f0;
		border-color: #fde2e2;
		color: #f56c6c;
	}

	// 表格样式优化
	:deep(.el-table) {
		.el-table__header-wrapper {
			.el-table__header {
				th {
					background-color: #f8f9fa;
					color: #606266;
					font-weight: 600;
				}
			}
		}

		.el-table__body-wrapper {
			.el-table__body {
				tr:hover {
					background-color: #f5f7fa;
				}
			}
		}
	}

	// 搜索表单样式
	.el-form--inline {
		.el-form-item {
			margin-bottom: 10px;
		}
	}
}
</style>

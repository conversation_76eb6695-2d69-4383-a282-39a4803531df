<template>
  <div class="select-none">
    <el-image :src="bg" class="wave" fit="cover" />
    <div class="absolute flex-c right-5 top-3"></div>
    <div class="login-container">
      <div class="login-box">
        <div class="login-form">
          <div class="my-3 text-6xl font-semibold login-title">{{ getThemeConfig.globalTitle }}</div>
          <div class="flex self-center justify-center">
            <div class="p-12 mx-auto bg-white rounded-3xl w-96">
              <div class="space-y-0">
                <password v-if="loginType === LoginTypeEnum.PASSWORD" @signInSuccess="signInSuccess" @change="changeLoginType" />
                <expire v-if="loginType === LoginTypeEnum.EXPIRE" :username="username" @change="changeLoginType" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="loginIndex">
import { useThemeConfig } from "/@/stores/themeConfig";
import { NextLoading } from "/@/utils/loading";
import bg from "/@/assets/login/login.png";
import { useI18n } from "vue-i18n";
import { formatAxis } from "/@/utils/formatTime";
import { useMessage } from "/@/hooks/message";
import { Session } from "/@/utils/storage";
import { initBackEndControlRoutes } from "/@/router/backEnd";
import { LoginTypeEnum } from "/@/api/login";

// 引入组件
const Password = defineAsyncComponent(() => import("./component/password.vue"));
const Mobile = defineAsyncComponent(() => import("./component/mobile.vue"));
const Social = defineAsyncComponent(() => import("./component/social.vue"));
const Register = defineAsyncComponent(() => import("./component/register.vue"));
const Expire = defineAsyncComponent(() => import("./component/expire.vue"));
const Tenant = defineAsyncComponent(() => import("./component/tenant.vue"));

// 定义变量内容
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// 登录方式
const loginType = ref(LoginTypeEnum.PASSWORD);
// 用户名
const username = ref("");

// 修改登录类型
const changeLoginType = (type: LoginTypeEnum, name?: string) => {
  loginType.value = type;
  if (name) {
    username.value = name;
  }
};

// 获取布局配置信息
const getThemeConfig = computed(() => {
  return themeConfig.value;
});

// 登录成功后的跳转处理事件
const signInSuccess = async () => {
  const isNoPower = await initBackEndControlRoutes();
  if (isNoPower) {
    useMessage().warning("抱歉，您没有登录权限");
    Session.clear();
  } else {
    // 初始化登录成功时间问候语
    let currentTimeInfo = formatAxis(new Date());
    if (route.query?.redirect) {
      router.push({
        path: <string>route.query?.redirect,
        query: Object.keys(<string>route.query?.params).length > 0 ? JSON.parse(<string>route.query?.params) : "",
      });
    } else {
      router.push("/");
    }
    // 登录成功提示
    const signInText = t("signInText");
    useMessage().success(`${currentTimeInfo}，${signInText}`);
    // 添加 loading，防止第一次进入界面时出现短暂空白
    NextLoading.start();
  }
};

// 页面加载时
onMounted(() => {
  NextLoading.done();
});
</script>
<style lang="scss" scoped>
.login-title {
  color: var(--el-color-primary);
  font-size: 55px;
  font-weight: 700;
  text-shadow: 5px 5px 10px rgba(0, 0, 0, 0.3), 5px 5px 10px rgba(255,255,255, 0.3);
  letter-spacing: 20px;
}
</style>

<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<div class="b-container" v-if="show">
        <img src="../../../assets/title.png" style="width: 100%" alt="">
        <el-card style="margin-bottom: 20px">
          <div class="title"><div style="display: flex;align-items: center"><div class="shu"></div>本周期在线议价</div><div style="color: #abbace"> </div></div>
          <img src="../../../assets/title1.png" style="width: 100%" alt="">
          <div style="display: flex;justify-content: center;margin: 40px 0 30px"><img src="../../../assets/img2.png" style="width: 80px" alt=""></div>
          <div style="display: flex;justify-content: center;">
            <span style="color: #21b26f">任务进行中</span>，报价结束时间：2025-08-04 18:00:00
          </div>
          <div style="display: flex;justify-content: center;padding: 30px 0">
            <el-button @click="show = false" round style="background: #21b26f;color: #fff;width: 300px;height: 50px;border-radius: 90px">立即进入</el-button>
          </div>
        </el-card>
        <el-card>
          <div class="title"><div style="display: flex;align-items: center"><div class="shu"></div>历史记录</div><div style="color: #abbace">查看全部</div></div>
          <div style="display: flex;justify-content: space-between;">
            <div v-for="i in 3" :key="i" class="box" style="font-size: 12px">
              <div style="font-weight: bolder;font-size: 14px;margin-bottom: 5px">测试0729(2025-08-03 00:00:00至2025-08-05 23:59:59)</div>
              <div style="margin-bottom: 5px">参与标段:鲜肉类,米面粮油</div>
              <div style="margin-bottom: 5px">应用周期:2025年32周(2025-08-03至2025-08-09)</div>
              <div style="margin-bottom: 20px">报价要求:1111</div>
              <div style="display: flex;justify-content: space-between;color: #21b26f">
                <div>议价商品</div>
                <div>报价记录</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      <div v-else>
        <img src="../../../assets/title2.png" style="width: 100%;margin-bottom: 50px" alt="">
        <div style="text-align: center;margin-bottom: 20px">报价倒计时</div>
        <div style="display: flex;justify-content: center;">
          <FlipDown :endDate="finishTime" @timeUp="commitExamTest" :type="3" :theme="2" :timeUnit="['天','时','分','秒']" class="flip-down" />
        </div>
        <div style="text-align: center;margin: 80px 20px;font-weight: bolder;font-size: 20px">测试0729</div>
        <div style="display: flex;justify-content: right;margin-bottom: 20px">项目编号：202507291414513729</div>
        <el-descriptions
            class="margin-top"
            :column="3"
            :size="size"
            border
        >
          <el-descriptions-item label="应用周期">2025年32周(2025-08-03至2025-08-09)</el-descriptions-item>
          <el-descriptions-item label="报价截止时间">2025-08-04 18:00:00</el-descriptions-item>
          <el-descriptions-item label="参与品类">米面粮油</el-descriptions-item>
          <el-descriptions-item label="报价要求">11234</el-descriptions-item>
        </el-descriptions>

        <div class="title"><div style="display: flex;align-items: center"><div class="shu"></div>采购详情</div><div style="color: #abbace"><el-button type="success" @click="show = true">返回</el-button></div></div>


        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane v-for="(item,index) in tabList" :label="item.label" :name="item.name" :key="index">
            <div style="display: flex;justify-content: space-between;">
              <el-input v-model="input1" style="width: 240px" placeholder="请输入商品名称" :suffix-icon="Search" />
              <div>
                <span style="color: #ff903a;display: inline-block;margin-right: 10px">报价商品（73）个</span>
                <el-button style="margin-right: 10px;background: #21b26f;" type="success">确认报价</el-button>
                <el-button style="margin-right: 10px;background: #21b26f;" type="success">导出</el-button>
                <el-button style="margin-right: 10px;background: #21b26f;" type="success">导入</el-button>
              </div>
            </div>
            <el-table :data="tableData" style="width: 100%">
              <el-table-column type="selection" width="55" />
              <el-table-column prop="index" label="序号" width="180" />
              <el-table-column prop="name" label="商品名称" width="180" />
              <el-table-column prop="name1" label="商品图片" width="180" >
                <template #default="scope">
                  <img src="../../../assets/img1.png" style="width: 80px" alt="" />
                </template>
              </el-table-column>
              <el-table-column prop="value1" label="卖点" />
              <el-table-column prop="value2" label="预估采购量" />
              <el-table-column prop="value3" label="单位" />
              <el-table-column prop="value4" label="验收标准" />
              <el-table-column prop="value5" label="上一轮报价" />
              <el-table-column prop="value6" label="期望价" />
              <el-table-column prop="value7" label="商家报价状态" />
              <el-table-column prop="value8" label="价格￥" >
                <template #default="scope">
                  <el-input v-model="input" type="number" style="width: 80px" placeholder="" />
                </template>
              </el-table-column>
            </el-table>

          </el-tab-pane>
        </el-tabs>

      </div>
		</div>

		<!-- 编辑、新增  -->
<!--		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />-->
	</div>
</template>

<script setup lang="ts" name="supplierOrderList">
import FlipDown from 'vue-flip-down';
import { Search } from '@element-plus/icons-vue'
// import { BasicTableProps, useTable } from '/@/hooks/table';
// import { fetchList, delObjs } from '/@/api/admin/organization';
// import { useMessage, useMessageBox } from '/@/hooks/message';

const show = ref(true)

const finishTime = new Date(2025, 7, 5)

const commitExamTest = () => {
  console.log("提交成功")
}

const input1 = ref('')
const activeName = ref('first')
const tabList = ref([
  {label: '米面粮油（73）', name: 'first'}
])

const tableData = [
  {index: '1', name: '金龙鱼麦粉', value1: '', value2: '1.00', value3: '袋', value4: '', value5: '0.00', value6: '0.00', value7: '待报价', value8: '0.00'},
  {index: '2', name: '金龙鱼麦粉', value1: '', value2: '1.00', value3: '袋', value4: '', value5: '0.00', value6: '0.00', value7: '待报价', value8: '0.00'},
  {index: '3', name: '金龙鱼麦粉', value1: '', value2: '1.00', value3: '袋', value4: '', value5: '0.00', value6: '0.00', value7: '待报价', value8: '0.00'},
  {index: '4', name: '金龙鱼麦粉', value1: '', value2: '1.00', value3: '袋', value4: '', value5: '0.00', value6: '0.00', value7: '待报价', value8: '0.00'},
  {index: '5', name: '金龙鱼麦粉', value1: '', value2: '1.00', value3: '袋', value4: '', value5: '0.00', value6: '0.00', value7: '待报价', value8: '0.00'},
  {index: '6', name: '金龙鱼麦粉', value1: '', value2: '1.00', value3: '袋', value4: '', value5: '0.00', value6: '0.00', value7: '待报价', value8: '0.00'},
  {index: '7', name: '金龙鱼麦粉', value1: '', value2: '1.00', value3: '袋', value4: '', value5: '0.00', value6: '0.00', value7: '待报价', value8: '0.00'},
  {index: '8', name: '金龙鱼麦粉', value1: '', value2: '1.00', value3: '袋', value4: '', value5: '0.00', value6: '0.00', value7: '待报价', value8: '0.00'},
  {index: '9', name: '金龙鱼麦粉', value1: '', value2: '1.00', value3: '袋', value4: '', value5: '0.00', value6: '0.00', value7: '待报价', value8: '0.00'},
]

// 引入组件
// const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义查询字典

// 定义变量内容
// const formDialogRef = ref();
// 搜索变量
// const queryRef = ref();
// const showSearch = ref(true);
// 多选变量
// const selectObjs = ref([]) as any;
// const multiple = ref(true);

// const state: BasicTableProps = reactive<BasicTableProps>({
// 	queryForm: {},
// 	pageList: fetchList,
// });

//  table hook
// const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
// const resetQuery = () => {
// 	// 清空搜索条件
// 	queryRef.value?.resetFields();
// 	// 清空多选
// 	selectObjs.value = [];
// 	getDataList();
// };

// 导出excel
// const exportExcel = () => {
// 	downBlobFile('/admin/organization/export', Object.assign(state.queryForm, { ids: selectObjs }), 'organization.xlsx');
// };

// 多选事件
// const selectionChangHandle = (objs: { id: string }[]) => {
// 	selectObjs.value = objs.map(({ id }) => id);
// 	multiple.value = !objs.length;
// };

// 删除操作
// const handleDelete = async (ids: string[]) => {
// 	try {
// 		await useMessageBox().confirm('此操作将永久删除');
// 	} catch {
// 		return;
// 	}
//
// 	try {
// 		await delObjs(ids);
// 		getDataList();
// 		useMessage().success('删除成功');
// 	} catch (err: any) {
// 		useMessage().error(err.msg);
// 	}
// };
</script>
<style lang="scss" scoped>
.layout-padding {
  .layout-padding-view{
    overflow: auto;
  }

.title{
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: bolder;
  margin: 20px 0 40px;
  .shu{
    background: #21b26f;
    width: 4px;
    height: 20px;
    border-radius: 5px;
    margin-right: 20px;
  }
}
  .box{
    width: 32.5%;
    background: #f6f6f6;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    padding: 30px 20px 10px;
  }
  :deep .el-tabs__item.is-active,:deep .el-tabs__item:hover {
    color: #00c56b;
  }
  :deep .el-tabs__active-bar{
    background-color: #00c56b;
  }

}
</style>

<script setup lang="ts">
import {Search} from "@element-plus/icons-vue";
import { onMounted, onUnmounted } from "vue";
import AMapLoader from "@amap/amap-jsapi-loader";

const input1 = ref('')
const activeName = ref('first')
const activeName1 = ref('first')
const tabList = ref([
  {label: '列表', name: 'first'},
  {label: '轨迹', name: 'first1'},
  {label: '视频', name: 'first2'},
  {label: '回放', name: 'first3'},
])
const tabList2 = ref([
  {label: '选中列表（3）', name: 'first'},
  {label: '紧急通报', name: 'first1'},
  {label: '所有报警（2）', name: 'first2'},
  {label: '设备消息（0）', name: 'first3'},
  {label: '媒体文件（0）', name: 'first4'},
  {label: '过期列表（1）', name: 'first5'},
])

const tableData = [
  {index: 1, value1: '在线', value2: '苏G136VG', value3: '2025-08-04 18:00:00', value4: '基站定位', value5: '2025-08-04 18:00:00', value6: '28.6', value7: '99', value8: '电量63%', value9: '江苏省连云港市海州区区徐杨街道东站高架辅路勒航织造:西南121米 勒航织造', value10: '5998765146843541', value11: '' },
  {index: 1, value1: '在线', value2: '苏G136VG', value3: '2025-08-04 18:00:00', value4: '基站定位', value5: '2025-08-04 18:00:00', value6: '28.6', value7: '99', value8: '电量63%', value9: '江苏省连云港市海州区区徐杨街道东站高架辅路勒航织造:西南121米 勒航织造', value10: '5998765146843541', value11: '低电量关机报警' },
  {index: 1, value1: '在线', value2: '苏G136VG', value3: '2025-08-04 18:00:00', value4: '基站定位', value5: '2025-08-04 18:00:00', value6: '28.6', value7: '99', value8: '电量63%', value9: '江苏省连云港市海州区区徐杨街道东站高架辅路勒航织造:西南121米 勒航织造', value10: '5998765146843541', value11: '低电量关机报警' },
]

const defaultProps = {
  children: 'children',
  label: 'label',
}
const data = [
  {
    id: 2,
    label: '温湿度(3/19)',
    children: [
      {id: 1, label: '苏G522VR'},
      {id: 2, label: '苏G522VR'},
      {id: 3, label: '苏G522VR'},
      {id: 4, label: '苏G522VR'},
      {id: 5, label: '苏G522VR'},
      {id: 6, label: '苏G522VR'},
      {id: 7, label: '苏G522VR'},
      {id: 8, label: '苏G522VR'},
      {id: 9, label: '苏G522VR'},
      {id: 10, label: '苏G522VR'},
      {id: 11, label: '苏G522VR'}
    ],
  },
  {
    id: 3,
    label: 'gps(1/19)',
    children: [
      {id: 31, label: '苏G522VR'},
      {id: 32, label: '苏G522VR'},
      {id: 33, label: '苏G522VR'},
      {id: 34, label: '苏G522VR'},
      {id: 35, label: '苏G522VR'},
      {id: 36, label: '苏G522VR'},
      {id: 37, label: '苏G522VR'},
      {id: 38, label: '苏G522VR'},
      {id: 39, label: '苏G522VR'},
      {id: 310, label: '苏G522VR'},
      {id: 311, label: '苏G522VR'}
    ],
  },
]

let map = null;

onMounted(() => {
  window._AMapSecurityConfig = {
    securityJsCode: "c6546ef29c2e4fbc940ec16bfc298079",
  };
  AMapLoader.load({
    key: "88ed2d3e65df8fec0f82307ff9ca1200", // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ["AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
  })
      .then((AMap) => {
        map = new AMap.Map("container", {
          // 设置地图容器id
          viewMode: "2D", // 是否为3D地图模式
          zoom: 14, // 初始化地图级别
          center: [119.227683,34.600833], // 初始化地图中心点位置
        });
        // 将 icon 传入 marker
        let endMarker = new AMap.Marker({
          position: new AMap.LngLat(119.227683,34.600833),
          icon: "https://a.amap.com/jsapi_demos/static/demo-center-v2/car.png",
          offset: new AMap.Pixel(-13, -26),
        });


        //鼠标点击marker弹出自定义的信息窗体
        endMarker.on('click', function () {
          infoWindow.open(map, endMarker.getPosition());
        });
        //实例化信息窗体
        var title = '苏G522VR<span style="font-size:11px;color:#1f7efe;">静止50分钟48秒</span>',
            content = [];
        content.push("<span>当日里程：</span><span style='color: #1e83e9'>42.77km</span>");
        content.push("<span>总里程：</span><span style='color: #1e83e9'>8542.77km</span>");
        content.push("<span>设备序号：10096001245</span>");
        content.push("<span>最后更新：2025-08-04 09:11:11</span>");
        content.push("<span>定位类型：基站定位/洪差范阁400米/信号96%</span>");
        content.push("<span>温度湿度：28.8'C 99%</span>");
        content.push("<span>设备状态：电量63%</span>");
        content.push("<span>江苏省连云港市海州区徐杨街道东站高架辅路勤航织造;西南1</span>");
        var infoWindow = new AMap.InfoWindow({
          isCustom: true,  //使用自定义窗体
          content: createInfoWindow(title, content.join("<br/>")),
          offset: new AMap.Pixel(220, -30)
        });

        //构建自定义信息窗体
        function createInfoWindow(title, content) {
          var info = document.createElement("div");
          info.className = "custom-info input-card content-window-card";

          //可以通过下面的方式修改自定义窗体的宽高
          info.style.width = "400px";
          // 定义顶部标题
          var top = document.createElement("div");
          var titleD = document.createElement("div");
          var closeX = document.createElement("img");
          top.className = "info-top";
          titleD.innerHTML = title;
          closeX.src = "https://webapi.amap.com/images/close2.gif";
          closeX.onclick = closeInfoWindow;

          top.appendChild(titleD);
          top.appendChild(closeX);
          info.appendChild(top);

          // 定义中部内容
          var middle = document.createElement("div");
          middle.className = "info-middle";
          middle.style.backgroundColor = 'white';
          middle.innerHTML = content;
          info.appendChild(middle);

          // 定义底部内容
          var bottom = document.createElement("div");
          bottom.className = "info-bottom";
          bottom.style.position = 'relative';
          bottom.style.top = '0px';
          bottom.style.margin = '0 auto';
          var sharp = document.createElement("img");
          sharp.src = "https://webapi.amap.com/images/sharp.png";
          bottom.appendChild(sharp);
          info.appendChild(bottom);
          return info;
        }

        //关闭信息窗体
        function closeInfoWindow() {
          map.clearInfoWindow();
        }



        infoWindow.open(map, endMarker.getPosition());
        map.add([endMarker]);
      })
      .catch((e) => {
        console.log(e);
      });
});

onUnmounted(() => {
  map?.destroy();
});

</script>

<template>
  <div class="gps">
    <img src="../../../assets/gps_title.png" style="width: 100%" alt="">
    <div class="gps-box">
      <div class="left">
        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane v-for="(item,index) in tabList" :label="item.label" :name="item.name" :key="index">
          </el-tab-pane>
          <div style="display: flex;justify-content: space-between;">
            <el-input v-model="input1" style="width: 240px" placeholder="账号" :suffix-icon="Search" />
          </div>
          <el-tree
              style="max-width: 600px"
              :data="data"
              show-checkbox
              node-key="id"
              :default-expanded-keys="[2, 3]"
              :default-checked-keys="[5]"
              :props="defaultProps"
          >
            <template #default="{ node }">
              <div class="custom-tree-node">
                <span>{{ node.label }}</span>
                <div v-if="node.label.indexOf('苏') > -1">关机4天15小时</div>
              </div>
            </template>
          </el-tree>
        </el-tabs>
      </div>
      <div class="right">
        <div class="right_top" id="container"></div>
        <div class="right_bottom">
          <el-tabs v-model="activeName1" class="demo-tabs">
            <el-tab-pane v-for="(item,index) in tabList2" :label="item.label" :name="item.name" :key="index">
            </el-tab-pane>
          </el-tabs>
          <el-table :data="tableData">
            <el-table-column prop="index" label="序号" />
            <el-table-column prop="value1" label="在线状态" />
            <el-table-column prop="value2" label="设备名称" />
            <el-table-column prop="value3" label="最后更新" />
            <el-table-column prop="value4" label="定位类型" />
            <el-table-column prop="value5" label="有效定位" />
            <el-table-column prop="value6" label="温度（℃）" />
            <el-table-column prop="value7" label="湿度" />
            <el-table-column prop="value8" label="状态" />
            <el-table-column prop="value9" label="地址" show-overflow-tooltip />
            <el-table-column prop="value10" label="ICCID" />
            <el-table-column prop="value11" label="报警" />
          </el-table>
        </div>

      </div>
    </div>
  </div>

</template>

<style scoped lang="scss">
.gps{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep .amap-info-contentContainer{
    .content-window-card {
      position: relative;
      box-shadow: none;
      bottom: 0;
      left: 0;
      width: auto;
      padding: 0;
    }

    .content-window-card p {
      height: 2rem;
    }

    .custom-info {
      border: solid 1px silver;
    }

    div.info-top {
      position: relative;
      background: none repeat scroll 0 0 #F9F9F9;
      border-bottom: 1px solid #CCC;
      border-radius: 5px 5px 0 0;
    }

    div.info-top div {
      display: inline-block;
      color: #333333;
      font-size: 14px;
      font-weight: bold;
      line-height: 31px;
      padding: 0 10px;
    }

    div.info-top img {
      position: absolute;
      top: 10px;
      right: 10px;
      transition-duration: 0.25s;
    }

    div.info-top img:hover {
      box-shadow: 0px 0px 5px #000;
    }

    div.info-middle {
      font-size: 12px;
      padding: 10px 6px;
      line-height: 20px;
    }

    div.info-bottom {
      height: 0px;
      width: 100%;
      clear: both;
      text-align: center;
    }

    div.info-bottom img {
      position: relative;
      z-index: 104;
    }

    span {
      margin-left: 5px;
      font-size: 11px;
    }

    .info-middle img {
      float: left;
      margin-right: 6px;
    }
  }

  .gps-box{
    width: 100%;
    flex: 1;
    display: flex;
    .left{
      width: 15%;
      height: 100%;
      background: #fff;
      padding: 20px;
    }
    .right{
      flex: 1;
      display: flex;
      flex-direction: column;
      .right_top{
        flex: 1;
        background: #3296fa;
      }
      .right_bottom{
        padding: 10px;
      }
    }

  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
  }
}
</style>

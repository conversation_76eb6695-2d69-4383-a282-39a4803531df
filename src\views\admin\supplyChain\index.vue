<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
          <el-form-item label="时间" prop="name">
            <el-date-picker
                v-model="state.queryForm.name"
                type="daterange"
                range-separator="→"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :size="size"
            />
          </el-form-item>
					<el-form-item label="检测项目" prop="name">
						<el-input placeholder="请输入检测项目" v-model="state.queryForm.name" />
					</el-form-item>
          <el-form-item label="样本三级名称" prop="name">
            <el-input placeholder="请输入样本三级名称" v-model="state.queryForm.name" />
          </el-form-item>
          <el-form-item label="来源单位（供应商）" prop="name">
            <el-input placeholder="请输入查询内容" v-model="state.queryForm.name" />
          </el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
						<el-button icon="Refresh" @click="resetQuery">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>
      <el-tabs v-model="activeName" class="demo-tabs">
        <el-tab-pane v-for="(item,index) in tabList" :label="item.label" :name="item.name" :key="index">
          <el-table :data="tableData" style="width: 100%">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="index" label="检测时间" width="180" />
            <el-table-column prop="name" label="来源单位（供应商）" />
            <el-table-column prop="value1" label="仪器编号" />
            <el-table-column prop="value2" label="样品一级名称" />
            <el-table-column prop="value3" label="样品二级名称" />
            <el-table-column prop="value4" label="样品三级名称" />
            <el-table-column prop="value5" label="检测项目" />
            <el-table-column prop="value6" label="检测结果" />
            <el-table-column prop="value7" label="检测人" />
            <el-table-column prop="value8" label="操作" width="300">
              <template #default="scope">
                <el-button type="success" link>绑定订单</el-button>
                <el-button type="success" link @click="dialogTableVisible = true">检测报告单</el-button>
                <el-button type="success" link>查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>

        </el-tab-pane>
      </el-tabs>

		</div>

		<el-dialog v-model="dialogTableVisible" width="1000">
			<el-descriptions class="margin-top" title="鸡腿肉(无骨)检测报告单" :column="3" :size="size" border>
				<el-descriptions-item label="检测项目">氯霉素</el-descriptions-item>
				<el-descriptions-item label="检测时间">2025-06-24 04:42:44</el-descriptions-item>
				<el-descriptions-item label="来源单位(供应商)">连云港东湖甄选供应链有限公司</el-descriptions-item>
				<el-descriptions-item label="仪器编号">5E23821304</el-descriptions-item>
				<el-descriptions-item label="样品一级名称">鲜肉类</el-descriptions-item>
				<el-descriptions-item label="样品二级名称">鲜肉类</el-descriptions-item>
				<el-descriptions-item label="样品三级名称">鸡腿肉(无骨)</el-descriptions-item>
				<el-descriptions-item label="域名称">ppb</el-descriptions-item>
				<el-descriptions-item label="采样code">218564562646</el-descriptions-item>
				<el-descriptions-item label="经营者">01</el-descriptions-item>
				<el-descriptions-item label="采样人">东湖甄选检测员</el-descriptions-item>
				<el-descriptions-item label="采样时间">2025-06-24 04:42:44</el-descriptions-item>
				<el-descriptions-item label="采样编码">25062404410104</el-descriptions-item>
				<el-descriptions-item label="检测单位">东湖甄选</el-descriptions-item>
				<el-descriptions-item label="检测结果数值">0</el-descriptions-item>
				<el-descriptions-item label="检测结果">阴性</el-descriptions-item>
				<el-descriptions-item label="检测人">东湖甄选检测员</el-descriptions-item>
			</el-descriptions>

		</el-dialog>

		<!-- 编辑、新增  -->
<!--		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />-->
	</div>
</template>

<script setup lang="ts" name="supplierOrderList">
import { BasicTableProps, useTable } from '/@/hooks/table';
// import { fetchList, delObjs } from '/@/api/admin/organization';
// import { useMessage, useMessageBox } from '/@/hooks/message';
// import {Search} from "@element-plus/icons-vue";

import {fetchList} from "/@/api/admin/organization";

const activeName = ref('first')
const dialogTableVisible = ref(false)
const tabList = ref([
  {label: '全部（7848）', name: 'first'}
])
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {}
});
const tableData = [
  {index: '2025-06-24 04:47:00', name: '连云港东湖甄选供应链有限公司', value1: '5E23821304', value2: '豆制品', value3: '豆制品', value4: '绿豆芽(芽长约5-10cm)', value5: '莱克多巴胺', value6: '阴性', value7: '东湖甄选检测员'},
  {index: '2025-06-24 04:47:00', name: '连云港东湖甄选供应链有限公司', value1: '5E23821304', value2: '豆制品', value3: '豆制品', value4: '绿豆芽(芽长约5-10cm)', value5: '莱克多巴胺', value6: '阴性', value7: '东湖甄选检测员'},
  {index: '2025-06-24 04:47:00', name: '连云港东湖甄选供应链有限公司', value1: '5E23821304', value2: '豆制品', value3: '豆制品', value4: '绿豆芽(芽长约5-10cm)', value5: '莱克多巴胺', value6: '阴性', value7: '东湖甄选检测员'},
  {index: '2025-06-24 04:47:00', name: '连云港东湖甄选供应链有限公司', value1: '5E23821304', value2: '豆制品', value3: '豆制品', value4: '绿豆芽(芽长约5-10cm)', value5: '莱克多巴胺', value6: '阴性', value7: '东湖甄选检测员'},
  {index: '2025-06-24 04:47:00', name: '连云港东湖甄选供应链有限公司', value1: '5E23821304', value2: '豆制品', value3: '豆制品', value4: '绿豆芽(芽长约5-10cm)', value5: '莱克多巴胺', value6: '阴性', value7: '东湖甄选检测员'},
  {index: '2025-06-24 04:47:00', name: '连云港东湖甄选供应链有限公司', value1: '5E23821304', value2: '豆制品', value3: '豆制品', value4: '绿豆芽(芽长约5-10cm)', value5: '莱克多巴胺', value6: '阴性', value7: '东湖甄选检测员'},
  {index: '2025-06-24 04:47:00', name: '连云港东湖甄选供应链有限公司', value1: '5E23821304', value2: '豆制品', value3: '豆制品', value4: '绿豆芽(芽长约5-10cm)', value5: '莱克多巴胺', value6: '阴性', value7: '东湖甄选检测员'},
  {index: '2025-06-24 04:47:00', name: '连云港东湖甄选供应链有限公司', value1: '5E23821304', value2: '豆制品', value3: '豆制品', value4: '绿豆芽(芽长约5-10cm)', value5: '莱克多巴胺', value6: '阴性', value7: '东湖甄选检测员'},
  {index: '2025-06-24 04:47:00', name: '连云港东湖甄选供应链有限公司', value1: '5E23821304', value2: '豆制品', value3: '豆制品', value4: '绿豆芽(芽长约5-10cm)', value5: '莱克多巴胺', value6: '阴性', value7: '东湖甄选检测员'},
  {index: '2025-06-24 04:47:00', name: '连云港东湖甄选供应链有限公司', value1: '5E23821304', value2: '豆制品', value3: '豆制品', value4: '绿豆芽(芽长约5-10cm)', value5: '莱克多巴胺', value6: '阴性', value7: '东湖甄选检测员'},
  {index: '2025-06-24 04:47:00', name: '连云港东湖甄选供应链有限公司', value1: '5E23821304', value2: '豆制品', value3: '豆制品', value4: '绿豆芽(芽长约5-10cm)', value5: '莱克多巴胺', value6: '阴性', value7: '东湖甄选检测员'}
]

// 引入组件
// const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义查询字典

// 定义变量内容
// const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// // 多选变量
const selectObjs = ref([]) as any;
// const multiple = ref(true);
//
// const state: BasicTableProps = reactive<BasicTableProps>({
// 	queryForm: {},
// 	pageList: fetchList,
// });

//  table hook
const { getDataList } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	getDataList();
};

// 导出excel
// const exportExcel = () => {
// 	downBlobFile('/admin/organization/export', Object.assign(state.queryForm, { ids: selectObjs }), 'organization.xlsx');
// };

// 多选事件
// const selectionChangHandle = (objs: { id: string }[]) => {
// 	selectObjs.value = objs.map(({ id }) => id);
// 	multiple.value = !objs.length;
// };

// 删除操作
// const handleDelete = async (ids: string[]) => {
// 	try {
// 		await useMessageBox().confirm('此操作将永久删除');
// 	} catch {
// 		return;
// 	}
//
// 	try {
// 		await delObjs(ids);
// 		getDataList();
// 		useMessage().success('删除成功');
// 	} catch (err: any) {
// 		useMessage().error(err.msg);
// 	}
// };
</script>
<style lang="scss" scoped>
.layout-padding {

  .demo-tabs-content{
    background: #f6fffa;
    height: 40px;
    padding: 0 20px;
    display: flex;
    align-items: center;
  }

  :deep .el-tabs__item.is-active,:deep .el-tabs__item:hover {
    color: #00c56b;
  }
  :deep .el-tabs__active-bar{
    background-color: #00c56b;
  }
}
</style>

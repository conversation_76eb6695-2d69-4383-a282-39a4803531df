<template>
  <div class="detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-row">
        <el-button type="text" icon="ArrowLeft" @click="goBack">返回</el-button>
        <span class="divider">|</span>
        <span class="order-number">订单编号：{{ orderDetail.orderNumber }}</span>
        <el-icon style="color: #04c66e; margin-left: 8px"><CopyDocument /></el-icon>
      </div>
      <div class="header-row">
        <span class="order-time">下单时间：{{ orderDetail.orderTime }}</span>
        <span class="delivery-time">预计送达时间：{{ orderDetail.deliveryTime }}</span>
        <span class="confirm-time">到货确认时间：{{ orderDetail.confirmTime }}</span>
      </div>
    </div>

    <!-- 基本信息总览 -->
    <el-card class="info-overview" style="margin-bottom: 20px;">
      <div class="overview-title">基本信息总览</div>
      <div class="overview-content">
        <!-- 收货人信息 -->
        <div class="info-section">
          <div class="section-title">收货人信息</div>
          <div class="info-item">
            <span class="label">收货人：</span>
            <span>{{ orderDetail.receiver }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系电话：</span>
            <span>{{ orderDetail.phone }}</span>
          </div>
          <div class="info-item">
            <span class="label">收货地址：</span>
            <span>{{ orderDetail.address }}</span>
          </div>
        </div>

        <!-- 配送信息 -->
        <div class="info-section">
          <div class="section-title">配送信息</div>
          <div class="delivery-status">
            <el-tag type="success" size="large">配送中</el-tag>
          </div>
          <div class="info-item">
            <span class="label">配送方式：</span>
            <span>{{ orderDetail.deliveryMethod }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系方式：</span>
            <span>{{ orderDetail.deliveryPhone }}</span>
          </div>
        </div>

        <!-- 配送员信息 -->
        <div class="info-section">
          <div class="section-title">配送员信息</div>
          <div class="info-item">
            <span class="label">配送员：</span>
            <span>{{ orderDetail.deliveryPerson }}</span>
          </div>
        </div>

        <!-- 采购单位信息 -->
        <div class="info-section">
          <div class="section-title">采购单位信息</div>
          <div class="info-item">
            <span class="label">采购单位：</span>
            <span>{{ orderDetail.purchaseUnit }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 商品信息 -->
    <el-card class="product-info" style="margin-bottom: 20px;">
      <div class="card-title">
        <span>商品信息</span>
        <span class="product-count">共{{ orderDetail.products.length }}个商品 2024-07-26 17:35:08 供货商下单</span>
      </div>

      <div class="table-container">
        <el-table :data="orderDetail.products" style="width: 100%">
          <el-table-column label="商品信息" width="180" fixed="left">
            <template #default="scope">
              <div class="product-cell">
                <img :src="scope.row.image" class="product-image" />
                <span>{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="category" label="配送方式" align="center" />
          <el-table-column prop="name" label="商品名称" align="center"/>
          <el-table-column prop="unit" label="单位" align="center" width="80" />
          <el-table-column prop="unitPrice" label="单价(元)" align="center" />
          <el-table-column prop="orderQuantity" label="订单量" align="center"  />
          <el-table-column prop="acceptQuantity" label="验收量" align="center" width="100" />
          <el-table-column prop="deliveryQuantity" label="发货量" align="center" width="100" />
          <el-table-column prop="smallUnit" label="小计(元)" align="center" width="100" />
          <el-table-column prop="weight" label="重量" align="center"  />
          <el-table-column prop="remark" label="备注" align="center" width="120" />
        </el-table>
      </div>

      <div class="total-amount">
        <div class="amount-item">
          <span>订单金额：</span>
          <span class="amount">¥{{ orderDetail.orderAmount }}</span>
        </div>
        <div class="amount-item">
          <span>实收金额：</span>
          <span class="amount">¥{{ orderDetail.actualAmount }}</span>
        </div>
      </div>
    </el-card>

    <!-- 商品生产情况 -->
    <el-card class="production-info" style="margin-bottom: 20px;">
      <div class="card-title">商品生产情况</div>

      <div class="table-container">
        <el-table :data="orderDetail.production" style="width: 100%">
          <el-table-column prop="name" label="商品名称" align="center"  fixed="left" />
          <el-table-column prop="productionStatus" label="该批次产品生产日期" align="center"  />
          <el-table-column prop="storageLocation" label="存储位置" align="center" width="180" />
          <el-table-column prop="qualityInspection" label="合格证号码日期" align="center" width="140" />
          <el-table-column prop="processingDate" label="建议日期" align="center" width="120" />
          <el-table-column prop="productionDate" label="生产日期" align="center" width="120" />
          <el-table-column prop="shelfLife" label="生产" align="center"  />
          <el-table-column prop="productionBatch" label="生产批号" align="center"   />
          <el-table-column prop="supplier" label="供货商" align="center"  />
          <el-table-column prop="status" label="状态" align="center" width="100" />
        </el-table>
      </div>
    </el-card>

    <!-- 供应商验收情况 -->
    <el-card class="supplier-acceptance" style="margin-bottom: 20px;">
      <div class="card-title">供应商验收情况</div>

      <div class="table-container">
        <el-table :data="orderDetail.acceptance" style="width: 100%">
          <el-table-column prop="name" label="商品名称" align="center" fixed="left" />
          <el-table-column prop="inspectionTime" label="验收时间" align="center" />
          <el-table-column prop="inspectionDate" label="验收日期" align="center" width="120" />
          <el-table-column prop="inspectionQuantity" label="验收数量" align="center" width="120" />
          <el-table-column prop="inspector" label="验收人" align="center"  />
          <el-table-column prop="inspectionResult" label="验收结果" align="center"  />
        </el-table>
      </div>
    </el-card>

    <!-- 车辆信息 -->
    <el-card class="vehicle-info" style="margin-bottom: 20px;">
      <div class="card-title">车辆信息</div>
      <div class="vehicle-content">
        <div class="vehicle-item">
          <span>车牌号码：{{ orderDetail.vehicleNumber }}</span>
        </div>
        <div class="vehicle-item">
          <span>司机姓名：{{ orderDetail.driverName }}</span>
        </div>
        <div class="vehicle-actions">
          <el-button type="success" size="small" icon="Phone">电话</el-button>
          <el-button type="primary" size="small" icon="Message">短信</el-button>
        </div>
      </div>
    </el-card>

    <!-- 日志 -->
    <el-card class="log-info">
      <div class="card-title">日志</div>

      <div class="table-container">
        <el-table :data="orderDetail.logs" style="width: 100%">
          <el-table-column prop="sequence" label="序号" align="center" width="80" fixed="left" />
          <el-table-column prop="time" label="时间" align="center"  />
          <el-table-column prop="operator" label="操作人" align="center"  />
          <el-table-column prop="operatorRole" label="操作人角色" align="center"  />
          <el-table-column prop="amount" label="操作金额" align="center"  />
          <el-table-column prop="orderNumber" label="操作订单" align="center"  />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="purchaseOrderDetail">
import { ArrowLeft, CopyDocument, Phone, Message } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";

const router = useRouter();

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 订单详情数据
const orderDetail = ref({
  orderNumber: "202507261735083414",
  orderTime: "2025-07-26 17:35:08 (星期六)",
  deliveryTime: "2025-07-26 17:35:08 (星期六)",
  confirmTime: "2025-07-26 17:35:08 (星期六)",
  receiver: "收货人",
  phone: "13333333333",
  address: "江苏省南京市玄武区中山东路9号",
  deliveryMethod: "配送中",
  deliveryPhone: "配送联系电话",
  deliveryPerson: "配送员小李",
  purchaseUnit: "采购单位名称",
  orderAmount: "1320",
  actualAmount: "1320",
  vehicleNumber: "苏A12345",
  driverName: "张师傅",
  products: [
    {
      image: "https://via.placeholder.com/60x60/67C23A/ffffff?text=五花肉",
      name: "五花肉",
      category: "新鲜蔬菜",
      unit: "斤",
      unitPrice: "15",
      orderQuantity: "88.00kg",
      acceptQuantity: "0.00kg",
      deliveryQuantity: "0.00kg",
      smallUnit: "1320",
      weight: "-",
      remark: "备注",
    },
  ],
  production: [
    {
      name: "五花肉",
      productionStatus: "-",
      storageLocation: "2025-07-07 至 2025-07-31",
      qualityInspection: "111",
      processingDate: "111",
      productionDate: "111",
      shelfLife: "111",
      productionBatch: "-",
      supplier: "-",
      status: "-",
    },
  ],
  acceptance: [
    {
      name: "五花肉",
      inspectionTime: "-",
      inspectionDate: "-",
      inspectionQuantity: "-",
      inspector: "-",
      inspectionResult: "-",
    },
  ],
  logs: [
    {
      sequence: 1,
      time: "2025-07-30 06:55:20",
      operator: "客户端提交",
      operatorRole: "采购单位管理员",
      amount: "56,142,168.55",
      orderNumber: "订单编号：202507261735083414",
    },
    {
      sequence: 2,
      time: "2025-07-28 17:51:23",
      operator: "客户端提交",
      operatorRole: "采购单位管理员",
      amount: "223,164,150.153",
      orderNumber: "订单编号：202507261735083414",
    },
  ],
});
</script>

<style lang="scss" scoped>
.detail {
  padding: 20px;
  background: #fff;
  .page-header {
    padding: 16px 0;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 20px;

    .header-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .divider {
        margin: 0 16px;
        color: #dcdfe6;
      }

      .order-number {
        font-weight: 600;
        color: #303133;
        margin-right: 20px;
      }

      .order-time,
      .delivery-time,
      .confirm-time {
        color: #909399;
        margin-right: 20px;
        font-size: 14px;
      }
    }
  }

  .overview-title,
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .overview-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;

    .info-section {
      .section-title {
        font-weight: 600;
        color: #303133;
        margin-bottom: 12px;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
      }

      .info-item {
        margin-bottom: 8px;

        .label {
          color: #909399;
          margin-right: 8px;
        }
      }

      .delivery-status {
        margin-bottom: 12px;
      }
    }
  }

  .table-container {
    overflow-x: auto;

    :deep(.el-table) {
      min-width: 1000px;
    }
  }

  .product-count {
    color: #909399;
    font-size: 14px;
    margin-left: 20px;
  }

  .product-cell {
    display: flex;
    align-items: center;

    .product-image {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      margin-right: 12px;
    }
  }

  .total-amount {
    display: flex;
    justify-content: flex-end;
    gap: 40px;
    padding: 16px 0;
    border-top: 1px solid #ebeef5;
    margin-top: 16px;

    .amount-item {
      .amount {
        color: #f56c6c;
        font-weight: 600;
        font-size: 16px;
      }
    }
  }

  .vehicle-content {
    display: flex;
    align-items: center;
    gap: 40px;
    flex-wrap: wrap;

    .vehicle-item {
      color: #606266;
    }

    .vehicle-actions {
      margin-left: auto;

      .el-button {
        margin-left: 12px;

        @media (max-width: 768px) {
          margin-left: 0;
          margin-right: 12px;
        }
      }
    }
  }

  :deep(.el-card__body) {
    padding: 20px;
  }

  :deep(.el-table) {
    .el-table__header {
      th {
        background-color: #f8f9fa;
        color: #606266;
        font-weight: 600;
      }
    }
  }

  :deep(.el-tag--success) {
    background-color: #f0f9ff;
    border-color: #c6f6d5;
    color: #67c23a;
  }
}
</style>

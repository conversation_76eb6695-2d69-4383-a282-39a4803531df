<template>
	<el-dialog :title="form.id ? '编辑商品' : '新增商品'" v-model="visible" :close-on-click-modal="false" draggable width="700">
		<el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="90px" v-loading="loading">
			<el-row :gutter="24">
				<el-col :span="12" class="mb20">
					<el-form-item label="商品名称" prop="name">
						<el-input v-model="form.name" placeholder="请输入商品名称" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="商品分类" prop="category">
						<el-select v-model="form.category" placeholder="请选择商品分类" style="width: 100%">
							<el-option label="蔬菜类" value="vegetables" />
							<el-option label="肉类" value="meat" />
							<el-option label="水果类" value="fruits" />
							<el-option label="调料类" value="seasoning" />
							<el-option label="粮食类" value="grain" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="24" class="mb20">
					<el-form-item label="商品图片" prop="images">
						<Image2 v-model="imagesUrl"></Image2>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="计量单位" prop="unit">
						<el-select v-model="form.unit" placeholder="请选择计量单位" style="width: 100%">
							<el-option label="斤" value="斤" />
							<el-option label="袋" value="袋" />
							<el-option label="瓶" value="瓶" />
							<el-option label="个" value="个" />
							<el-option label="盒" value="盒" />
							<el-option label="包" value="包" />
						</el-select>
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="单价(元)" prop="price">
						<el-input-number v-model="form.price" :precision="2" :min="0" :max="9999" style="width: 100%" placeholder="请输入单价" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="库存数量" prop="stock">
						<el-input-number v-model="form.stock" :min="0" :max="99999" style="width: 100%" placeholder="请输入库存数量" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="保质期(天)" prop="shelfLife">
						<el-input-number v-model="form.shelfLife" :min="1" :max="3650" style="width: 100%" placeholder="请输入保质期" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="供应商" prop="supplier">
						<el-input v-model="form.supplier" placeholder="请输入供应商名称" />
					</el-form-item>
				</el-col>

				<el-col :span="12" class="mb20">
					<el-form-item label="商品状态" prop="status">
						<el-radio-group v-model="form.status">
							<el-radio value="active">上架</el-radio>
							<el-radio value="inactive">下架</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="visible = false">取消</el-button>
				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="GoodsDialog">
import { useMessage } from '/@/hooks/message';
import Image2 from '/@/components/Upload/Image2.vue';
import IMG1 from "/@/assets/img1.png";

const emit = defineEmits(['refresh']);
const imagesUrl = ref<any>([]);
// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 提交表单数据
const form = reactive({
	id: '',
	name: '',
	images: '',
	category: '',
	unit: '',
	price: 0,
	stock: 0,
	supplier: '',
	shelfLife: 1,
	status: 'active'
});

// 定义校验规则
const dataRules = ref({
	name: [{ required: true, message: '商品名称不能为空', trigger: 'blur' }],
	category: [{ required: true, message: '商品分类不能为空', trigger: 'change' }],
	unit: [{ required: true, message: '计量单位不能为空', trigger: 'change' }],
	price: [{ required: true, message: '单价不能为空', trigger: 'blur' }],
	stock: [{ required: true, message: '库存数量不能为空', trigger: 'blur' }],
	supplier: [{ required: true, message: '供应商不能为空', trigger: 'blur' }],
	shelfLife: [{ required: true, message: '保质期不能为空', trigger: 'blur' }],
	status: [{ required: true, message: '商品状态不能为空', trigger: 'change' }]
});

// 打开弹窗
const openDialog = (id: string) => {
	visible.value = true;
	form.id = '';
	imagesUrl.value = [];

	// 重置表单数据
	nextTick(() => {
		dataFormRef.value?.resetFields();
	});

	// 如果是编辑，获取商品信息（这里使用模拟数据）
	if (id) {
		form.id = id;
		getGoodsData(id);
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;
	try {
		loading.value = true;
		if (imagesUrl.value.length) {
			form.images = imagesUrl.value.join();
		}

		// 模拟提交成功
		await new Promise(resolve => setTimeout(resolve, 500));

		useMessage().success(form.id ? '修改成功' : '添加成功');
		visible.value = false;
		emit('refresh');
	} catch (err: any) {
		useMessage().error(err.msg || '操作失败');
	} finally {
		loading.value = false;
	}
};

// 初始化表单数据（模拟获取数据）
const getGoodsData = (id: string) => {
	loading.value = true;

	// 模拟获取数据
	setTimeout(() => {
		// 这里可以根据id获取对应的商品数据
		const mockData = {
			id: id,
			name: '新鲜白菜',
			images: IMG1,
			category: 'vegetables',
			unit: '斤',
			price: 3.50,
			stock: 150,
			supplier: '绿色农场',
			shelfLife: 7,
			status: 'active'
		};

		Object.assign(form, mockData);
		if (mockData.images) {
			imagesUrl.value = mockData.images.split(',');
		}
		loading.value = false;
	}, 300);
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>

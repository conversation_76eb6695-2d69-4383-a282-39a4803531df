<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <el-row v-show="showSearch">
        <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
          <el-form-item label="商品名称" prop="name">
            <el-input placeholder="请输入商品名称" v-model="state.queryForm.name" />
          </el-form-item>
          <el-form-item label="商品分类" prop="category">
            <el-select placeholder="请选择商品分类" v-model="state.queryForm.category" clearable>
              <el-option label="蔬菜类" value="vegetables" />
              <el-option label="肉类" value="meat" />
              <el-option label="水果类" value="fruits" />
              <el-option label="调料类" value="seasoning" />
              <el-option label="粮食类" value="grain" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div class="mb8" style="width: 100%">
          <el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()" v-auth="'admin_goods_add'"> 新 增 </el-button>
          <el-button plain :disabled="multiple" icon="Delete" type="primary" v-auth="'admin_goods_del'" @click="handleDelete(selectObjs)"> 删除 </el-button>
          <right-toolbar v-model:showSearch="showSearch" :export="'admin_goods_export'" @exportExcel="exportExcel" class="ml10 mr20" style="float: right" @queryTable="getDataList"></right-toolbar>
        </div>
      </el-row>
      <el-table
        :data="state.dataList"
        v-loading="state.loading"
        border
        :cell-style="tableStyle.cellStyle"
        :header-cell-style="tableStyle.headerCellStyle"
        @selection-change="selectionChangHandle"
        @sort-change="sortChangeHandle"
      >
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column prop="name" label="商品名称" show-overflow-tooltip />
        <el-table-column prop="images" label="商品图片" width="180">
          <template #default="scope">
            <div class="img-box" v-if="scope.row.images">
              <el-image :src="scope.row.images" :preview-src-list="[scope.row.images]" fit="cover" :preview-teleported="true" :hide-on-click-modal="true"></el-image>
            </div>
            <div class="noPic" v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="商品分类" show-overflow-tooltip>
          <template #default="scope">
            <el-tag :type="getCategoryType(scope.row.category)">{{ getCategoryName(scope.row.category) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="计量单位" show-overflow-tooltip />
        <el-table-column prop="price" label="单价(元)" show-overflow-tooltip>
          <template #default="scope">
            <span class="price">¥{{ scope.row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="库存数量" show-overflow-tooltip>
          <template #default="scope">
            <el-tag :type="scope.row.stock > 10 ? 'success' : scope.row.stock > 0 ? 'warning' : 'danger'">
              {{ scope.row.stock }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="supplier" label="供应商" show-overflow-tooltip />
        <el-table-column prop="shelfLife" label="保质期(天)" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" show-overflow-tooltip>
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === "active" ? "上架" : "下架" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button icon="edit-pen" text type="primary" @click="formDialogRef.openDialog(scope.row.id)">编辑 </el-button>
            <el-button icon="delete" text type="primary" @click="handleDelete([scope.row.id])">删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
    </div>

    <!-- 编辑、新增  -->
    <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
  </div>
</template>

<script setup lang="ts" name="goodsList">
import { BasicTableProps, useTable } from "/@/hooks/table";
import { useMessage, useMessageBox } from "/@/hooks/message";
import IMG1 from "/@/assets/img1.png";
import IMG3 from "/@/assets/img3.jpg";
import IMG4 from "/@/assets/img4.png";
import IMG5 from "/@/assets/img5.png";
import IMG6 from "/@/assets/img6.png";
import IMG7 from "/@/assets/img7.png";
import IMG8 from "/@/assets/img8.png";
import IMG9 from "/@/assets/img9.png";
import IMG10 from "/@/assets/img10.png";
import IMG11 from "/@/assets/img11.png";
import IMG12 from "/@/assets/img12.png";
import IMG13 from '/@/assets/img13.png';

// 引入组件
const FormDialog = defineAsyncComponent(() => import("./form.vue"));

// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

// 写死的食材商品数据
const mockGoodsData = [
  {
    id: "1",
    name: "新鲜白菜",
    images: IMG3,
    category: "vegetables",
    unit: "斤",
    price: "3.50",
    stock: 150,
    supplier: "绿色农场",
    shelfLife: 7,
    status: "active",
    createTime: "2024-01-15 10:30:00",
  },
  {
    id: "2",
    name: "优质猪肉",
    images: IMG1,
    category: "meat",
    unit: "斤",
    price: "28.00",
    stock: 50,
    supplier: "优质肉类供应商",
    shelfLife: 3,
    status: "active",
    createTime: "2024-01-15 09:20:00",
  },
  {
    id: "3",
    name: "新鲜苹果",
    images: IMG12,
    category: "fruits",
    unit: "斤",
    price: "8.80",
    stock: 80,
    supplier: "果园直供",
    shelfLife: 15,
    status: "active",
    createTime: "2024-01-14 16:45:00",
  },
  {
    id: "4",
    name: "生抽酱油",
    images: IMG7,
    category: "seasoning",
    unit: "瓶",
    price: "12.50",
    stock: 5,
    supplier: "调料批发商",
    shelfLife: 365,
    status: "active",
    createTime: "2024-01-14 14:20:00",
  },
  {
    id: "5",
    name: "优质大米",
    images: IMG13,
    category: "grain",
    unit: "袋",
    price: "45.00",
    stock: 0,
    supplier: "粮食供应商",
    shelfLife: 180,
    status: "inactive",
    createTime: "2024-01-13 11:10:00",
  },
  {
    id: "6",
    name: "新鲜土豆",
    images: IMG6,
    category: "vegetables",
    unit: "斤",
    price: "2.80",
    stock: 200,
    supplier: "绿色农场",
    shelfLife: 30,
    status: "active",
    createTime: "2024-01-13 08:30:00",
  },
  {
    id: "7",
    name: "新鲜鸡蛋",
    images: IMG5,
    category: "meat",
    unit: "个",
    price: "1.20",
    stock: 300,
    supplier: "养殖场直供",
    shelfLife: 21,
    status: "active",
    createTime: "2024-01-12 15:20:00",
  },
  {
    id: "8",
    name: "香蕉",
    images: IMG12,
    category: "fruits",
    unit: "斤",
    price: "6.50",
    stock: 12,
    supplier: "果园直供",
    shelfLife: 5,
    status: "active",
    createTime: "2024-01-12 13:45:00",
  },
  {
    id: "9",
    name: "新鲜胡萝卜",
    images: IMG10,
    category: "vegetables",
    unit: "斤",
    price: "4.20",
    stock: 120,
    supplier: "绿色农场",
    shelfLife: 14,
    status: "active",
    createTime: "2024-01-11 14:30:00",
  },
  {
    id: "10",
    name: "新鲜牛肉",
    images: IMG11,
    category: "meat",
    unit: "斤",
    price: "45.00",
    stock: 25,
    supplier: "优质肉类供应商",
    shelfLife: 2,
    status: "active",
    createTime: "2024-01-11 11:20:00",
  },
  {
    id: "11",
    name: "新鲜橙子",
    images: IMG12,
    category: "fruits",
    unit: "斤",
    price: "7.80",
    stock: 90,
    supplier: "果园直供",
    shelfLife: 10,
    status: "active",
    createTime: "2024-01-10 16:15:00",
  },
  {
    id: "12",
    name: "老抽酱油",
    images: IMG1,
    category: "seasoning",
    unit: "瓶",
    price: "15.80",
    stock: 30,
    supplier: "调料批发商",
    shelfLife: 365,
    status: "active",
    createTime: "2024-01-10 13:40:00",
  },
  {
    id: "13",
    name: "优质面粉",
    images: IMG3,
    category: "grain",
    unit: "袋",
    price: "25.00",
    stock: 60,
    supplier: "粮食供应商",
    shelfLife: 120,
    status: "active",
    createTime: "2024-01-09 10:25:00",
  },
  {
    id: "14",
    name: "新鲜西红柿",
    images: IMG4,
    category: "vegetables",
    unit: "斤",
    price: "5.60",
    stock: 180,
    supplier: "绿色农场",
    shelfLife: 8,
    status: "active",
    createTime: "2024-01-09 08:50:00",
  },
  {
    id: "15",
    name: "新鲜鸡胸肉",
    images: IMG5,
    category: "meat",
    unit: "斤",
    price: "18.50",
    stock: 40,
    supplier: "养殖场直供",
    shelfLife: 3,
    status: "active",
    createTime: "2024-01-08 15:30:00",
  },
  {
    id: "16",
    name: "新鲜葡萄",
    images: IMG6,
    category: "fruits",
    unit: "斤",
    price: "12.80",
    stock: 8,
    supplier: "果园直供",
    shelfLife: 7,
    status: "active",
    createTime: "2024-01-08 12:20:00",
  },
  {
    id: "17",
    name: "花椒粉",
    images: IMG7,
    category: "seasoning",
    unit: "包",
    price: "8.50",
    stock: 45,
    supplier: "调料批发商",
    shelfLife: 180,
    status: "active",
    createTime: "2024-01-07 14:10:00",
  },
  {
    id: "18",
    name: "优质小米",
    images: IMG8,
    category: "grain",
    unit: "袋",
    price: "35.00",
    stock: 35,
    supplier: "粮食供应商",
    shelfLife: 150,
    status: "active",
    createTime: "2024-01-07 11:45:00",
  },
  {
    id: "19",
    name: "新鲜黄瓜",
    images: IMG9,
    category: "vegetables",
    unit: "斤",
    price: "3.80",
    stock: 160,
    supplier: "绿色农场",
    shelfLife: 6,
    status: "active",
    createTime: "2024-01-06 16:30:00",
  },
  {
    id: "20",
    name: "新鲜羊肉",
    images: IMG10,
    category: "meat",
    unit: "斤",
    price: "52.00",
    stock: 15,
    supplier: "优质肉类供应商",
    shelfLife: 2,
    status: "active",
    createTime: "2024-01-06 13:15:00",
  },
  {
    id: "21",
    name: "新鲜梨子",
    images: IMG11,
    category: "fruits",
    unit: "斤",
    price: "6.20",
    stock: 70,
    supplier: "果园直供",
    shelfLife: 12,
    status: "active",
    createTime: "2024-01-05 15:40:00",
  },
  {
    id: "22",
    name: "香醋",
    images: IMG12,
    category: "seasoning",
    unit: "瓶",
    price: "9.80",
    stock: 55,
    supplier: "调料批发商",
    shelfLife: 730,
    status: "active",
    createTime: "2024-01-05 12:25:00",
  },
  {
    id: "23",
    name: "优质燕麦",
    images: IMG1,
    category: "grain",
    unit: "袋",
    price: "28.00",
    stock: 0,
    supplier: "粮食供应商",
    shelfLife: 180,
    status: "inactive",
    createTime: "2024-01-04 10:50:00",
  },
  {
    id: "24",
    name: "新鲜菠菜",
    images: IMG3,
    category: "vegetables",
    unit: "斤",
    price: "4.50",
    stock: 95,
    supplier: "绿色农场",
    shelfLife: 5,
    status: "active",
    createTime: "2024-01-04 08:20:00",
  },
];

// 模拟获取数据的方法
const fetchMockList = async (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockGoodsData];

      // 根据搜索条件过滤数据
      if (params.name) {
        filteredData = filteredData.filter((item) => item.name.toLowerCase().includes(params.name.toLowerCase()));
      }

      if (params.category) {
        filteredData = filteredData.filter((item) => item.category === params.category);
      }

      // 分页处理
      const current = params.current || 1;
      const size = params.size || 10;
      const start = (current - 1) * size;
      const end = start + size;
      const records = filteredData.slice(start, end);

      resolve({
        data: {
          records,
          total: filteredData.length,
          current,
          size,
        },
      });
    }, 300);
  });
};

const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {},
  pageList: fetchMockList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 获取分类名称
const getCategoryName = (category: string) => {
  const categoryMap: Record<string, string> = {
    vegetables: "蔬菜类",
    meat: "肉类",
    fruits: "水果类",
    seasoning: "调料类",
    grain: "粮食类",
  };
  return categoryMap[category] || category;
};

// 获取分类标签类型
const getCategoryType = (category: string) => {
  const typeMap: Record<string, string> = {
    vegetables: "success",
    meat: "danger",
    fruits: "warning",
    seasoning: "info",
    grain: "primary",
  };
  return typeMap[category] || "";
};

// 清空搜索条件
const resetQuery = () => {
  // 清空搜索条件
  queryRef.value?.resetFields();
  // 清空多选
  selectObjs.value = [];
  getDataList();
};

// 导出excel
const exportExcel = () => {
  downBlobFile("/admin/goods/export", Object.assign(state.queryForm, { ids: selectObjs }), "goods.xlsx");
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
  selectObjs.value = objs.map(({ id }) => id);
  multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
  try {
    await useMessageBox().confirm("此操作将永久删除");
  } catch {
    return;
  }

  try {
    // 模拟删除操作
    useMessage().success("删除成功");
    getDataList();
  } catch (err: any) {
    useMessage().error(err.msg);
  }
};

// 初始化加载数据
onMounted(() => {
  getDataList();
});
</script>
<style lang="scss" scoped>
.img-box {
  display: flex;
  flex-direction: row;

  .el-image {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }
}

.price {
  color: #f56c6c;
  font-weight: bold;
}
</style>

<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row v-show="showSearch">
				<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
					<el-form-item label="采购单位名称" prop="unitName">
						<el-input placeholder="请输入采购单位名称" v-model="state.queryForm.unitName" />
					</el-form-item>
					<el-form-item label="采购单位类型" prop="unitType">
						<el-select placeholder="请选择采购单位类型" v-model="state.queryForm.unitType" clearable>
							<el-option label="学校" value="school" />
							<el-option label="企业" value="enterprise" />
							<el-option label="政府机关" value="government" />
							<el-option label="医院" value="hospital" />
						</el-select>
					</el-form-item>
					<el-form-item label="采购计划编号" prop="planNumber">
						<el-input placeholder="请输入采购计划编号" v-model="state.queryForm.planNumber" />
					</el-form-item>
					<el-form-item label="采购方式" prop="purchaseMethod">
						<el-select placeholder="请选择采购方式" v-model="state.queryForm.purchaseMethod" clearable>
							<el-option label="在线采购" value="online" />
							<el-option label="线下采购" value="offline" />
							<el-option label="招标采购" value="bidding" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button icon="search" type="primary" @click="getDataList"> 查询</el-button>
						<el-button icon="Refresh" @click="resetQuery">重置</el-button>
					</el-form-item>
				</el-form>
			</el-row>

			<!-- 状态标签栏 -->
			<el-row class="mb15">
				<el-tag
					v-for="(item, index) in statusTabs"
					:key="index"
					:type="activeStatus === item.value ? 'success' : 'info'"
					:effect="activeStatus === item.value ? 'dark' : 'plain'"
					class="status-tag"
					@click="handleStatusChange(item.value)"
				>
					{{ item.label }}
				</el-tag>
			</el-row>

			<el-row>
				<div class="mb8" style="width: 100%">
					<el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()">
						新 增
					</el-button>
					<el-button plain :disabled="multiple" icon="Delete" type="primary" @click="handleDelete(selectObjs)">
						删除
					</el-button>
					<right-toolbar
						v-model:showSearch="showSearch"
						:export="'purchase_plan_export'"
						@exportExcel="exportExcel"
						class="ml10 mr20"
						style="float: right"
						@queryTable="getDataList"
					></right-toolbar>
				</div>
			</el-row>
			<el-table
				:data="filteredTableData"
				v-loading="state.loading"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				@selection-change="selectionChangHandle"
			>
				<el-table-column type="selection" width="55" align="center" />
				<el-table-column prop="unitName" label="采购单位名称" show-overflow-tooltip />
				<el-table-column prop="unitType" label="采购单位类型">
					<template #default="scope">
						<el-tag :type="getUnitTypeTag(scope.row.unitType)">{{ getUnitTypeText(scope.row.unitType) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="buildingTime" label="建设时间" />
				<el-table-column prop="planNumber" label="采购计划编号" />
				<el-table-column prop="planAmount" label="计划金额">
					<template #default="scope">
						<span class="amount-text">{{ scope.row.planAmount }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="purchaseMethod" label="采购方式" width="120">
					<template #default="scope">
						<el-tag :type="getPurchaseMethodTag(scope.row.purchaseMethod)">{{ getPurchaseMethodText(scope.row.purchaseMethod) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="configStatus" label="配置状态" width="100">
					<template #default="scope">
						<el-tag :type="getConfigStatusTag(scope.row.configStatus)">{{ getConfigStatusText(scope.row.configStatus) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="planStatus" label="计划状态" width="100">
					<template #default="scope">
						<el-tag :type="getPlanStatusTag(scope.row.planStatus)">{{ getPlanStatusText(scope.row.planStatus) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="200">
					<template #default="scope">
						<el-button type="success" link @click="formDialogRef.openDialog(scope.row.id)">详情</el-button>
						<el-button type="success" link @click="viewDetail(scope.row)">查看</el-button>
						<el-button type="success" link @click="handleDelete([scope.row.id])">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
			<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
		</div>

		<!-- 编辑、新增  -->
		<form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
	</div>
</template>

<script setup lang="ts" name="purchasePlan">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));

// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);
// 当前状态
const activeStatus = ref('all');

// 状态标签
const statusTabs = ref([
	{ label: '全部', value: 'all' },
	{ label: '配置完成', value: 'configured' },
	{ label: '已提交', value: 'submitted' },
	{ label: '已绑定', value: 'bound' }
]);

// 模拟数据
const tableData = ref([
	{
		id: '1',
		unitName: '阳光小学(学生) - 幼儿园班级',
		unitType: 'school',
		buildingTime: '2025-07-26 17:34:50',
		planNumber: 'CGH+2025070072',
		planAmount: 40000,
		purchaseMethod: 'online',
		configStatus: 'completed',
		planStatus: 'submitted'
	},
	{
		id: '2',
		unitName: '阳光小学(学生)',
		unitType: 'school',
		buildingTime: '2025-07-26 17:36:50',
		planNumber: 'CGH+2025070071',
		planAmount: 30000,
		purchaseMethod: 'offline',
		configStatus: 'completed',
		planStatus: 'bound'
	},
	{
		id: '3',
		unitName: '阳光小学(学生) - 1',
		unitType: 'school',
		buildingTime: '2025-07-26 17:36:50',
		planNumber: 'CGH+2025070070',
		planAmount: 30000,
		purchaseMethod: 'offline',
		configStatus: 'completed',
		planStatus: 'bound'
	},
	{
		id: '4',
		unitName: '阳光小学(学生) - 幼儿园',
		unitType: 'school',
		buildingTime: '',
		planNumber: 'CGH+2025070069',
		planAmount: 30000,
		purchaseMethod: 'online',
		configStatus: 'pending',
		planStatus: 'draft'
	},
	{
		id: '5',
		unitName: '阳光小学(学生) - 产品选择',
		unitType: 'school',
		buildingTime: '',
		planNumber: 'CGH+2025070068',
		planAmount: 30000,
		purchaseMethod: 'offline',
		configStatus: 'completed',
		planStatus: 'draft'
	},
	{
		id: '6',
		unitName: '阳光小学(学生) - 高中开学',
		unitType: 'school',
		buildingTime: '',
		planNumber: 'CGH+2025070067',
		planAmount: 20000,
		purchaseMethod: 'online',
		configStatus: 'pending',
		planStatus: 'draft'
	},
	{
		id: '7',
		unitName: '阳光小学(学生) - 测试2',
		unitType: 'school',
		buildingTime: '2025-07-10 10:06:22',
		planNumber: 'CGH+2025070062',
		planAmount: 30000,
		purchaseMethod: 'offline',
		configStatus: 'completed',
		planStatus: 'bound'
	},
	{
		id: '8',
		unitName: '阳光小学(学生) - 测试1',
		unitType: 'school',
		buildingTime: '2025-07-10 10:07:53',
		planNumber: 'CGH+2025070061',
		planAmount: 30000,
		purchaseMethod: 'offline',
		configStatus: 'completed',
		planStatus: 'bound'
	},
	{
		id: '9',
		unitName: '阳光小学(学生)',
		unitType: 'school',
		buildingTime: '',
		planNumber: 'CGH+2025070060',
		planAmount: 30000,
		purchaseMethod: 'offline',
		configStatus: 'pending',
		planStatus: 'draft'
	},
	{
		id: '10',
		unitName: '阳光小学(学生) - ceshi2',
		unitType: 'school',
		buildingTime: '2025-07-10 08:50:53',
		planNumber: 'CGH+2025070059',
		planAmount: 30000,
		purchaseMethod: 'offline',
		configStatus: 'completed',
		planStatus: 'bound'
	},
	{
		id: '11',
		unitName: '阳光小学(学生) - ceshi1',
		unitType: 'school',
		buildingTime: '2025-07-10 08:50:11',
		planNumber: 'CGH+2025070058',
		planAmount: 30000,
		purchaseMethod: 'offline',
		configStatus: 'completed',
		planStatus: 'bound'
	},
	{
		id: '12',
		unitName: '阳光小学(学生)',
		unitType: 'school',
		buildingTime: '2025-07-09 20:37:05',
		planNumber: 'CGH+2025070057',
		planAmount: 30000,
		purchaseMethod: 'offline',
		configStatus: 'completed',
		planStatus: 'bound'
	}
]);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {},
	loading: false,
	pagination: {
		total: tableData.value.length,
		current: 1,
		size: 10
	}
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 根据状态过滤数据
const filteredTableData = computed(() => {
	if (activeStatus.value === 'all') {
		return tableData.value;
	}

	const statusMap: Record<string, string[]> = {
		configured: ['completed'],
		submitted: ['submitted'],
		bound: ['bound']
	};

	const targetStatuses = statusMap[activeStatus.value] || [];
	return tableData.value.filter(item => targetStatuses.includes(item.planStatus));
});

// 状态切换
const handleStatusChange = (status: string) => {
	activeStatus.value = status;
};

// 获取单位类型标签颜色
const getUnitTypeTag = (type: string) => {
	const tagMap: Record<string, string> = {
		school: 'primary',
		enterprise: 'success',
		government: 'warning',
		hospital: 'info'
	};
	return tagMap[type] || 'default';
};

// 获取单位类型文本
const getUnitTypeText = (type: string) => {
	const textMap: Record<string, string> = {
		school: '学校',
		enterprise: '企业',
		government: '政府机关',
		hospital: '医院'
	};
	return textMap[type] || '未知';
};

// 获取采购方式标签颜色
const getPurchaseMethodTag = (method: string) => {
	const tagMap: Record<string, string> = {
		online: 'success',
		offline: 'warning',
		bidding: 'info'
	};
	return tagMap[method] || 'default';
};

// 获取采购方式文本
const getPurchaseMethodText = (method: string) => {
	const textMap: Record<string, string> = {
		online: '在线采购',
		offline: '线下采购',
		bidding: '招标采购'
	};
	return textMap[method] || '未知';
};

// 获取配置状态标签颜色
const getConfigStatusTag = (status: string) => {
	const tagMap: Record<string, string> = {
		completed: 'success',
		pending: 'warning',
		failed: 'danger'
	};
	return tagMap[status] || 'default';
};

// 获取配置状态文本
const getConfigStatusText = (status: string) => {
	const textMap: Record<string, string> = {
		completed: '配置完成',
		pending: '配置中',
		failed: '配置失败'
	};
	return textMap[status] || '未知';
};

// 获取计划状态标签颜色
const getPlanStatusTag = (status: string) => {
	const tagMap: Record<string, string> = {
		draft: 'info',
		submitted: 'warning',
		bound: 'success',
		cancelled: 'danger'
	};
	return tagMap[status] || 'default';
};

// 获取计划状态文本
const getPlanStatusText = (status: string) => {
	const textMap: Record<string, string> = {
		draft: '草稿',
		submitted: '已提交',
		bound: '已绑定',
		cancelled: '已取消'
	};
	return textMap[status] || '未知';
};

// 查看详情
const viewDetail = (row: any) => {
	useMessage().info(`查看 ${row.unitName} 的详细信息`);
};

// 清空搜索条件
const resetQuery = () => {
	// 清空搜索条件
	queryRef.value?.resetFields();
	// 清空多选
	selectObjs.value = [];
	// 重新加载数据
	state.loading = false;
};

// 导出excel
const exportExcel = () => {
	useMessage().success('导出功能开发中...');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
	selectObjs.value = objs.map(({ id }) => id);
	multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
	try {
		await useMessageBox().confirm('此操作将永久删除选中的采购计划，是否继续？');
	} catch {
		return;
	}

	try {
		// 模拟删除操作
		tableData.value = tableData.value.filter(item => !ids.includes(item.id));
		state.pagination.total = tableData.value.length;
		useMessage().success('删除成功');
		// 清空选择
		selectObjs.value = [];
		multiple.value = true;
	} catch (err: any) {
		useMessage().error('删除失败');
	}
};
</script>
<style lang="scss" scoped>
.layout-padding {

	// 状态标签样式
	.status-tag {
		margin-right: 10px;
		margin-bottom: 10px;
		cursor: pointer;
		transition: all 0.3s;

		&:hover {
			transform: translateY(-1px);
		}
	}

	// 金额文本样式
	.amount-text {
		color: #00c56b;
		font-weight: 600;
	}

	// 参考供应链页面的样式
	:deep(.el-button--text.el-button--success) {
		color: #00c56b;

		&:hover {
			color: #00a85a;
		}
	}

	:deep(.el-tag--primary) {
		background-color: #ecf5ff;
		border-color: #d9ecff;
		color: #409eff;
	}

	:deep(.el-tag--success) {
		background-color: #f0f9ff;
		border-color: #c6f6d5;
		color: #00c56b;
	}

	:deep(.el-tag--warning) {
		background-color: #fdf6ec;
		border-color: #faecd8;
		color: #e6a23c;
	}

	:deep(.el-tag--info) {
		background-color: #f4f4f5;
		border-color: #e9e9eb;
		color: #909399;
	}

	:deep(.el-tag--danger) {
		background-color: #fef0f0;
		border-color: #fde2e2;
		color: #f56c6c;
	}

	// 表格样式优化
	:deep(.el-table) {
		.el-table__header-wrapper {
			.el-table__header {
				th {
					background-color: #f8f9fa;
					color: #606266;
					font-weight: 600;
				}
			}
		}

		.el-table__body-wrapper {
			.el-table__body {
				tr:hover {
					background-color: #f5f7fa;
				}
			}
		}
	}

	// 搜索表单样式
	.el-form--inline {
		.el-form-item {
			margin-bottom: 10px;
		}
	}
}
</style>
